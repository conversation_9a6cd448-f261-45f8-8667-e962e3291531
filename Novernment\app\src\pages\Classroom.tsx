import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { getClassroomDetails } from '../utils/solana';

const Classroom = () => {
    const { classroomId } = useParams();
    const [classroom, setClassroom] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchClassroomDetails = async () => {
            try {
                const details = await getClassroomDetails(classroomId);
                setClassroom(details);
            } catch (error) {
                console.error("Error fetching classroom details:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchClassroomDetails();
    }, [classroomId]);

    if (loading) {
        return <div>Loading...</div>;
    }

    if (!classroom) {
        return <div>No classroom found.</div>;
    }

    return (
        <div>
            <h1>{classroom.name}</h1>
            <h2>Enrolled Students:</h2>
            <ul>
                {classroom.students.map(student => (
                    <li key={student.id}>{student.name} - Grade: {student.grade}</li>
                ))}
            </ul>
        </div>
    );
};

export default Classroom;