{"rustc": 383397013764560953, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 15657897354478470176, "path": 7160925608580564764, "deps": [[2828590642173593838, "cfg_if", false, 3792137565522295096], [4684437522915235464, "libc", false, 13151738663823140194], [5170503507811329045, "build_script_build", false, 5875601476379449368]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/getrandom-6f97ae51ecfb503f/dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}