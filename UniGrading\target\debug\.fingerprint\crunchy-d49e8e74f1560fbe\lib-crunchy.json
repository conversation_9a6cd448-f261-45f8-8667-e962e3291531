{"rustc": 383397013764560953, "features": "[\"default\", \"limit_128\"]", "declared_features": "[\"default\", \"limit_1024\", \"limit_128\", \"limit_2048\", \"limit_256\", \"limit_512\", \"limit_64\", \"std\"]", "target": 9963013543797884993, "profile": 15657897354478470176, "path": 9056838566486212868, "deps": [[5148925301303650630, "build_script_build", false, 7842102825052604882]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/crunchy-d49e8e74f1560fbe/dep-lib-crunchy", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}