{"rustc": 383397013764560953, "features": "[]", "declared_features": "[]", "target": 1643282199907566603, "profile": 15657897354478470176, "path": 1892639274906687477, "deps": [[9556858120010252096, "solana_transaction_error", false, 16812687930414739825], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [13452659356252768829, "solana_signature", false, 12647450157593019679]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-signer-62823e18235e6dfb/dep-lib-solana_signer", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}