import React from 'react';
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom';
import Home from './pages/Home';
import Classroom from './pages/Classroom';
import WalletConnect from './components/WalletConnect';
import './App.css';

const App: React.FC = () => {
    return (
        <Router>
            <div className="App">
                <WalletConnect />
                <Switch>
                    <Route path="/" exact component={Home} />
                    <Route path="/classroom/:id" component={Classroom} />
                </Switch>
            </div>
        </Router>
    );
};

export default App;