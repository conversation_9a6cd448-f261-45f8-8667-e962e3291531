# Novernment - A Solana-based Grades and Classroom Management Application

Novernment is a decentralized application built on the Solana blockchain for managing grades and classrooms. This project utilizes React.js for the frontend, integrating Solana Web3.js for blockchain interactions and wallet adapter functionality.

## Project Structure

The project is organized into two main directories: `app` for the frontend and `programs` for the backend Solana program.

### Frontend (app)

- **public/index.html**: The main HTML entry point for the React application.
- **src/components**: Contains reusable React components:
  - **ClassroomList.tsx**: Displays a list of classrooms.
  - **GradeManager.tsx**: Manages grades for students.
  - **WalletConnect.tsx**: Handles wallet connection functionality.
- **src/pages**: Contains page components:
  - **Home.tsx**: The homepage of the application.
  - **Classroom.tsx**: Displays details about a specific classroom.
- **src/utils/solana.ts**: Utility functions for interacting with the Solana blockchain.
- **src/App.tsx**: The main application component that sets up routing.
- **src/index.tsx**: The entry point for the React application.
- **src/react-app-env.d.ts**: TypeScript definitions for the React application environment.
- **package.json**: Configuration file for npm, listing dependencies and scripts.

### Backend (programs)

- **src/lib.rs**: Contains the main logic of the Solana program for managing classrooms and grades.
- **src/state.rs**: Defines data structures and state management for the Solana program.
- **Cargo.toml**: Configuration file for the Rust project.
- **Xargo.toml**: Used for building the Rust project with custom dependencies.

### Migrations and Tests

- **migrations/deploy.ts**: Script for deploying the Solana program.
- **tests/novernment.ts**: Tests for the Solana program functionality.

## Getting Started

To get started with the Novernment project, follow these steps:

1. **Clone the repository**:
   ```
   git clone <repository-url>
   cd novernment
   ```

2. **Install dependencies**:
   - For the frontend:
     ```
     cd app
     npm install
     ```
   - For the backend:
     ```
     cd programs/novernment
     cargo build
     ```

3. **Run the frontend**:
   ```
   npm start
   ```

4. **Deploy the Solana program**:
   ```
   cd migrations
   ts-node deploy.ts
   ```

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for details.