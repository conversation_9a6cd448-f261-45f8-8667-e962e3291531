{"rustc": 383397013764560953, "features": "[\"bincode\", \"bytemuck\", \"serde\"]", "declared_features": "[\"bincode\", \"bytemuck\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 16616187019842477339, "profile": 6652793396284126538, "path": 17846754942143984364, "deps": [[65234016722529558, "bincode", false, 12460930568304774582], [6511429716036861196, "bytemuck", false, 9004261617411574230], [9689903380558560274, "serde", false, 8537716410839897940], [10570260326288551891, "solana_instruction", false, 6543215718921772342], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [14591356476411885690, "solana_sdk_ids", false, 1263336053477103343], [16041962814414187897, "solana_clock", false, 8585465566309786899], [16257276029081467297, "serde_derive", false, 110293047266484015], [16847021361644352524, "solana_slot_hashes", false, 3431542754501008019]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-address-lookup-table-interface-9d98f46a77d864e9/dep-lib-solana_address_lookup_table_interface", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}