/**
 * Program IDL in camelCase format in order to be used in JS/TS.
 *
 * Note that this is only a type helper and is not the actual IDL. The original
 * IDL can be found at `target/idl/uni_grading.json`.
 */
export type UniGrading = {
  "address": "3D6Ap5VnrXHCNBek628nqw295z4gza49tFdUcoxdvTJw",
  "metadata": {
    "name": "uniGrading",
    "version": "0.1.0",
    "spec": "0.1.0",
    "description": "Created with Anchor"
  },
  "instructions": [
    {
      "name": "addStudent",
      "discriminator": [
        31,
        147,
        62,
        4,
        151,
        7,
        185,
        32
      ],
      "accounts": [
        {
          "name": "classroom",
          "writable": true
        },
        {
          "name": "student",
          "writable": true,
          "signer": true
        },
        {
          "name": "authority",
          "writable": true,
          "signer": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "studentName",
          "type": "string"
        },
        {
          "name": "studentId",
          "type": "string"
        }
      ]
    },
    {
      "name": "assignGrade",
      "discriminator": [
        171,
        174,
        208,
        133,
        192,
        129,
        145,
        126
      ],
      "accounts": [
        {
          "name": "student",
          "writable": true
        },
        {
          "name": "teacher",
          "writable": true,
          "signer": true
        },
        {
          "name": "teacherUser",
          "docs": [
            "Optional: Teacher user account for role verification"
          ],
          "optional": true
        },
        {
          "name": "clock",
          "address": "SysvarC1ock11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "assignmentName",
          "type": "string"
        },
        {
          "name": "grade",
          "type": "u8"
        },
        {
          "name": "maxGrade",
          "type": "u8"
        }
      ]
    },
    {
      "name": "deactivateUser",
      "discriminator": [
        170,
        53,
        163,
        46,
        104,
        99,
        39,
        15
      ],
      "accounts": [
        {
          "name": "user",
          "writable": true
        },
        {
          "name": "authority",
          "signer": true
        }
      ],
      "args": []
    },
    {
      "name": "deleteGrade",
      "discriminator": [
        51,
        62,
        20,
        94,
        201,
        36,
        181,
        114
      ],
      "accounts": [
        {
          "name": "student",
          "writable": true
        },
        {
          "name": "teacher",
          "writable": true,
          "signer": true
        },
        {
          "name": "teacherUser",
          "docs": [
            "Optional: Teacher user account for role verification"
          ],
          "optional": true
        }
      ],
      "args": [
        {
          "name": "assignmentName",
          "type": "string"
        }
      ]
    },
    {
      "name": "getClassroomInfo",
      "discriminator": [
        144,
        108,
        149,
        4,
        138,
        131,
        108,
        37
      ],
      "accounts": [
        {
          "name": "classroom"
        }
      ],
      "args": []
    },
    {
      "name": "getStudentGrades",
      "discriminator": [
        180,
        154,
        242,
        45,
        176,
        10,
        235,
        11
      ],
      "accounts": [
        {
          "name": "student"
        }
      ],
      "args": []
    },
    {
      "name": "getStudentList",
      "discriminator": [
        71,
        173,
        204,
        227,
        235,
        23,
        157,
        17
      ],
      "accounts": [
        {
          "name": "classroom"
        }
      ],
      "args": []
    },
    {
      "name": "getUserInfo",
      "discriminator": [
        216,
        140,
        210,
        76,
        46,
        9,
        66,
        81
      ],
      "accounts": [
        {
          "name": "user"
        }
      ],
      "args": []
    },
    {
      "name": "initializeClassroom",
      "discriminator": [
        71,
        222,
        90,
        122,
        21,
        108,
        119,
        178
      ],
      "accounts": [
        {
          "name": "classroom",
          "writable": true,
          "signer": true
        },
        {
          "name": "authority",
          "writable": true,
          "signer": true
        },
        {
          "name": "teacherUser",
          "docs": [
            "Optional: Teacher user account for role verification"
          ],
          "optional": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "classroomName",
          "type": "string"
        },
        {
          "name": "course",
          "type": "string"
        }
      ]
    },
    {
      "name": "registerUser",
      "discriminator": [
        2,
        241,
        150,
        223,
        99,
        214,
        116,
        97
      ],
      "accounts": [
        {
          "name": "user",
          "writable": true,
          "signer": true
        },
        {
          "name": "authority",
          "writable": true,
          "signer": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        },
        {
          "name": "clock",
          "address": "SysvarC1ock11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "username",
          "type": "string"
        },
        {
          "name": "role",
          "type": {
            "defined": {
              "name": "userRole"
            }
          }
        }
      ]
    },
    {
      "name": "removeStudent",
      "discriminator": [
        211,
        213,
        6,
        187,
        74,
        206,
        19,
        4
      ],
      "accounts": [
        {
          "name": "classroom",
          "writable": true
        },
        {
          "name": "student",
          "writable": true
        },
        {
          "name": "authority",
          "writable": true,
          "signer": true
        }
      ],
      "args": []
    },
    {
      "name": "updateUserProfile",
      "discriminator": [
        79,
        75,
        114,
        130,
        68,
        123,
        180,
        11
      ],
      "accounts": [
        {
          "name": "user",
          "writable": true
        },
        {
          "name": "authority",
          "signer": true
        }
      ],
      "args": [
        {
          "name": "newUsername",
          "type": "string"
        }
      ]
    }
  ],
  "accounts": [
    {
      "name": "classroom",
      "discriminator": [
        144,
        64,
        161,
        5,
        131,
        76,
        32,
        29
      ]
    },
    {
      "name": "student",
      "discriminator": [
        173,
        194,
        250,
        75,
        154,
        20,
        81,
        57
      ]
    },
    {
      "name": "user",
      "discriminator": [
        159,
        117,
        95,
        227,
        239,
        151,
        58,
        236
      ]
    }
  ],
  "errors": [
    {
      "code": 6000,
      "name": "invalidGrade",
      "msg": "Invalid grade value"
    },
    {
      "code": 6001,
      "name": "studentNotInClassroom",
      "msg": "Student not found in classroom"
    },
    {
      "code": 6002,
      "name": "unauthorized",
      "msg": "Unauthorized access"
    },
    {
      "code": 6003,
      "name": "invalidUsername",
      "msg": "Invalid username"
    },
    {
      "code": 6004,
      "name": "usernameTooLong",
      "msg": "Username too long"
    },
    {
      "code": 6005,
      "name": "userDeactivated",
      "msg": "User is deactivated"
    },
    {
      "code": 6006,
      "name": "onlyTeachers",
      "msg": "Only teachers can perform this action"
    },
    {
      "code": 6007,
      "name": "studentAlreadyExists",
      "msg": "Student already exists in classroom"
    },
    {
      "code": 6008,
      "name": "invalidAssignmentName",
      "msg": "Invalid assignment name"
    },
    {
      "code": 6009,
      "name": "assignmentNameTooLong",
      "msg": "Assignment name too long"
    },
    {
      "code": 6010,
      "name": "invalidMaxGrade",
      "msg": "Invalid max grade"
    }
  ],
  "types": [
    {
      "name": "classroom",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "name",
            "type": "string"
          },
          {
            "name": "course",
            "type": "string"
          },
          {
            "name": "teacher",
            "type": "pubkey"
          },
          {
            "name": "students",
            "type": {
              "vec": {
                "defined": {
                  "name": "studentRef"
                }
              }
            }
          }
        ]
      }
    },
    {
      "name": "grade",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "assignmentName",
            "type": "string"
          },
          {
            "name": "grade",
            "type": "u8"
          },
          {
            "name": "maxGrade",
            "type": "u8"
          },
          {
            "name": "timestamp",
            "type": "i64"
          },
          {
            "name": "gradedBy",
            "type": "pubkey"
          }
        ]
      }
    },
    {
      "name": "student",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "id",
            "type": "string"
          },
          {
            "name": "name",
            "type": "string"
          },
          {
            "name": "grades",
            "type": {
              "vec": {
                "defined": {
                  "name": "grade"
                }
              }
            }
          },
          {
            "name": "classroom",
            "type": "pubkey"
          }
        ]
      }
    },
    {
      "name": "studentRef",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "name",
            "type": "string"
          },
          {
            "name": "pubkey",
            "type": "pubkey"
          }
        ]
      }
    },
    {
      "name": "user",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "authority",
            "type": "pubkey"
          },
          {
            "name": "username",
            "type": "string"
          },
          {
            "name": "role",
            "type": {
              "defined": {
                "name": "userRole"
              }
            }
          },
          {
            "name": "createdAt",
            "type": "i64"
          },
          {
            "name": "isActive",
            "type": "bool"
          }
        ]
      }
    },
    {
      "name": "userRole",
      "type": {
        "kind": "enum",
        "variants": [
          {
            "name": "teacher"
          },
          {
            "name": "student"
          }
        ]
      }
    }
  ]
};
