{"address": "AUb7ZQUCsWSVu4ok5CfGeDbgyQTGcgn9WSsC4PwN7MBj", "metadata": {"name": "unigrading", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "add_student", "discriminator": [31, 147, 62, 4, 151, 7, 185, 32], "accounts": [{"name": "classroom", "writable": true}, {"name": "student", "writable": true, "signer": true}, {"name": "authority", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "student_name", "type": "string"}, {"name": "student_id", "type": "string"}]}, {"name": "assign_grade", "discriminator": [171, 174, 208, 133, 192, 129, 145, 126], "accounts": [{"name": "student", "writable": true}, {"name": "teacher", "writable": true, "signer": true}, {"name": "teacher_user", "docs": ["Optional: Teacher user account for role verification"], "optional": true}, {"name": "clock", "address": "SysvarC1ock11111111111111111111111111111111"}], "args": [{"name": "assignment_name", "type": "string"}, {"name": "grade", "type": "u8"}, {"name": "max_grade", "type": "u8"}]}, {"name": "deactivate_user", "discriminator": [170, 53, 163, 46, 104, 99, 39, 15], "accounts": [{"name": "user", "writable": true}, {"name": "authority", "signer": true}], "args": []}, {"name": "delete_grade", "discriminator": [51, 62, 20, 94, 201, 36, 181, 114], "accounts": [{"name": "student", "writable": true}, {"name": "teacher", "writable": true, "signer": true}, {"name": "teacher_user", "docs": ["Optional: Teacher user account for role verification"], "optional": true}], "args": [{"name": "assignment_name", "type": "string"}]}, {"name": "get_classroom_info", "discriminator": [144, 108, 149, 4, 138, 131, 108, 37], "accounts": [{"name": "classroom"}], "args": []}, {"name": "get_student_grades", "discriminator": [180, 154, 242, 45, 176, 10, 235, 11], "accounts": [{"name": "student"}], "args": []}, {"name": "get_student_list", "discriminator": [71, 173, 204, 227, 235, 23, 157, 17], "accounts": [{"name": "classroom"}], "args": []}, {"name": "get_user_info", "discriminator": [216, 140, 210, 76, 46, 9, 66, 81], "accounts": [{"name": "user"}], "args": []}, {"name": "initialize_classroom", "discriminator": [71, 222, 90, 122, 21, 108, 119, 178], "accounts": [{"name": "classroom", "writable": true, "signer": true}, {"name": "authority", "writable": true, "signer": true}, {"name": "teacher_user", "docs": ["Optional: Teacher user account for role verification"], "optional": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "classroom_name", "type": "string"}, {"name": "course", "type": "string"}]}, {"name": "register_user", "discriminator": [2, 241, 150, 223, 99, 214, 116, 97], "accounts": [{"name": "user", "writable": true, "signer": true}, {"name": "authority", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "clock", "address": "SysvarC1ock11111111111111111111111111111111"}], "args": [{"name": "username", "type": "string"}, {"name": "role", "type": {"defined": {"name": "UserRole"}}}]}, {"name": "remove_student", "discriminator": [211, 213, 6, 187, 74, 206, 19, 4], "accounts": [{"name": "classroom", "writable": true}, {"name": "student", "writable": true}, {"name": "authority", "writable": true, "signer": true}], "args": []}, {"name": "update_user_profile", "discriminator": [79, 75, 114, 130, 68, 123, 180, 11], "accounts": [{"name": "user", "writable": true}, {"name": "authority", "signer": true}], "args": [{"name": "new_username", "type": "string"}]}], "accounts": [{"name": "Classroom", "discriminator": [144, 64, 161, 5, 131, 76, 32, 29]}, {"name": "Student", "discriminator": [173, 194, 250, 75, 154, 20, 81, 57]}, {"name": "User", "discriminator": [159, 117, 95, 227, 239, 151, 58, 236]}], "errors": [{"code": 6000, "name": "InvalidGrade", "msg": "Invalid grade value"}, {"code": 6001, "name": "StudentNotInClassroom", "msg": "Student not found in classroom"}, {"code": 6002, "name": "Unauthorized", "msg": "Unauthorized access"}, {"code": 6003, "name": "InvalidUsername", "msg": "Invalid username"}, {"code": 6004, "name": "UsernameTooLong", "msg": "Username too long"}, {"code": 6005, "name": "UserDeactivated", "msg": "User is deactivated"}, {"code": 6006, "name": "OnlyTeachers", "msg": "Only teachers can perform this action"}, {"code": 6007, "name": "StudentAlreadyExists", "msg": "Student already exists in classroom"}, {"code": 6008, "name": "InvalidAssignmentName", "msg": "Invalid assignment name"}, {"code": 6009, "name": "AssignmentNameTooLong", "msg": "Assignment name too long"}, {"code": 6010, "name": "InvalidMaxGrade", "msg": "Invalid max grade"}], "types": [{"name": "Classroom", "type": {"kind": "struct", "fields": [{"name": "name", "type": "string"}, {"name": "course", "type": "string"}, {"name": "teacher", "type": "pubkey"}, {"name": "students", "type": {"vec": {"defined": {"name": "StudentRef"}}}}]}}, {"name": "Grade", "type": {"kind": "struct", "fields": [{"name": "assignment_name", "type": "string"}, {"name": "grade", "type": "u8"}, {"name": "max_grade", "type": "u8"}, {"name": "timestamp", "type": "i64"}, {"name": "graded_by", "type": "pubkey"}]}}, {"name": "Student", "type": {"kind": "struct", "fields": [{"name": "id", "type": "string"}, {"name": "name", "type": "string"}, {"name": "grades", "type": {"vec": {"defined": {"name": "Grade"}}}}, {"name": "classroom", "type": "pubkey"}]}}, {"name": "StudentRef", "type": {"kind": "struct", "fields": [{"name": "name", "type": "string"}, {"name": "pubkey", "type": "pubkey"}]}}, {"name": "User", "type": {"kind": "struct", "fields": [{"name": "authority", "type": "pubkey"}, {"name": "username", "type": "string"}, {"name": "role", "type": {"defined": {"name": "UserRole"}}}, {"name": "created_at", "type": "i64"}, {"name": "is_active", "type": "bool"}]}}, {"name": "UserRole", "type": {"kind": "enum", "variants": [{"name": "Teacher"}, {"name": "Student"}]}}]}