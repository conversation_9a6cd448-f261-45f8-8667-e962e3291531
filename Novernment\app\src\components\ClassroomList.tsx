import React, { useEffect, useState } from 'react';
import { getClassrooms } from '../utils/solana';

const ClassroomList: React.FC = () => {
    const [classrooms, setClassrooms] = useState([]);

    useEffect(() => {
        const fetchClassrooms = async () => {
            const data = await getClassrooms();
            setClassrooms(data);
        };

        fetchClassrooms();
    }, []);

    return (
        <div>
            <h2>Classroom List</h2>
            <ul>
                {classrooms.map((classroom) => (
                    <li key={classroom.id}>
                        <a href={`/classroom/${classroom.id}`}>{classroom.name}</a>
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default ClassroomList;