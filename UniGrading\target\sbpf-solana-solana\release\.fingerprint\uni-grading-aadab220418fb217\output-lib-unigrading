{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `custom-heap`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"src/lib.rs","byte_start":8894,"byte_end":8917,"line_start":224,"line_end":224,"column_start":23,"column_end":46,"is_primary":true,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/lib.rs","byte_start":5320,"byte_end":5350,"line_start":139,"line_end":139,"column_start":9,"column_end":39,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"programs/uni-grading/src/lib.rs","byte_start":92,"byte_end":102,"line_start":5,"line_end":5,"column_start":1,"column_end":11,"is_primary":false,"text":[{"text":"#[program]","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"programs/uni-grading/src/lib.rs","byte_start":92,"byte_end":102,"line_start":5,"line_end":5,"column_start":1,"column_end":11,"is_primary":false,"text":[{"text":"#[program]","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[program]","def_site_span":{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/anchor-attribute-program-0.31.1/src/lib.rs","byte_start":293,"byte_end":409,"line_start":12,"line_end":15,"column_start":1,"column_end":29,"is_primary":false,"text":[{"text":"pub fn program(","highlight_start":1,"highlight_end":16},{"text":"    _args: proc_macro::TokenStream,","highlight_start":1,"highlight_end":36},{"text":"    input: proc_macro::TokenStream,","highlight_start":1,"highlight_end":36},{"text":") -> proc_macro::TokenStream {","highlight_start":1,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"anchor_lang::solana_program::entrypoint!","def_site_span":{"file_name":"src/lib.rs","byte_start":4846,"byte_end":4869,"line_start":128,"line_end":128,"column_start":1,"column_end":24,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::custom_heap_default!","def_site_span":{"file_name":"src/lib.rs","byte_start":8825,"byte_end":8857,"line_start":222,"line_end":222,"column_start":1,"column_end":33,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"expected values for `feature` are: `anchor-debug`, `cpi`, `default`, `idl-build`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `custom-heap` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unexpected_cfgs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `custom-heap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprograms/uni-grading/src/lib.rs:5:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[program]\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected values for `feature` are: `anchor-debug`, `cpi`, `default`, `idl-build`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `custom-heap` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unexpected_cfgs)]` on by default\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the macro `$crate::custom_heap_default` which comes from the expansion of the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `custom-panic`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"src/lib.rs","byte_start":11084,"byte_end":11108,"line_start":281,"line_end":281,"column_start":23,"column_end":47,"is_primary":true,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/lib.rs","byte_start":5360,"byte_end":5391,"line_start":140,"line_end":140,"column_start":9,"column_end":40,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"programs/uni-grading/src/lib.rs","byte_start":92,"byte_end":102,"line_start":5,"line_end":5,"column_start":1,"column_end":11,"is_primary":false,"text":[{"text":"#[program]","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"programs/uni-grading/src/lib.rs","byte_start":92,"byte_end":102,"line_start":5,"line_end":5,"column_start":1,"column_end":11,"is_primary":false,"text":[{"text":"#[program]","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[program]","def_site_span":{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/anchor-attribute-program-0.31.1/src/lib.rs","byte_start":293,"byte_end":409,"line_start":12,"line_end":15,"column_start":1,"column_end":29,"is_primary":false,"text":[{"text":"pub fn program(","highlight_start":1,"highlight_end":16},{"text":"    _args: proc_macro::TokenStream,","highlight_start":1,"highlight_end":36},{"text":"    input: proc_macro::TokenStream,","highlight_start":1,"highlight_end":36},{"text":") -> proc_macro::TokenStream {","highlight_start":1,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"anchor_lang::solana_program::entrypoint!","def_site_span":{"file_name":"src/lib.rs","byte_start":4846,"byte_end":4869,"line_start":128,"line_end":128,"column_start":1,"column_end":24,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::custom_panic_default!","def_site_span":{"file_name":"src/lib.rs","byte_start":11014,"byte_end":11047,"line_start":279,"line_end":279,"column_start":1,"column_end":34,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"expected values for `feature` are: `anchor-debug`, `cpi`, `default`, `idl-build`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `custom-panic` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `custom-panic`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprograms/uni-grading/src/lib.rs:5:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[program]\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected values for `feature` are: `anchor-debug`, `cpi`, `default`, `idl-build`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `custom-panic` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the macro `$crate::custom_panic_default` which comes from the expansion of the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `anchor_lang::prelude::AccountInfo::<'a>::realloc`: Use AccountInfo::resize() instead","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"programs/uni-grading/src/lib.rs","byte_start":92,"byte_end":102,"line_start":5,"line_end":5,"column_start":1,"column_end":11,"is_primary":true,"text":[{"text":"#[program]","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"programs/uni-grading/src/lib.rs","byte_start":92,"byte_end":102,"line_start":5,"line_end":5,"column_start":1,"column_end":11,"is_primary":false,"text":[{"text":"#[program]","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[program]","def_site_span":{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/anchor-attribute-program-0.31.1/src/lib.rs","byte_start":293,"byte_end":409,"line_start":12,"line_end":15,"column_start":1,"column_end":29,"is_primary":false,"text":[{"text":"pub fn program(","highlight_start":1,"highlight_end":16},{"text":"    _args: proc_macro::TokenStream,","highlight_start":1,"highlight_end":36},{"text":"    input: proc_macro::TokenStream,","highlight_start":1,"highlight_end":36},{"text":") -> proc_macro::TokenStream {","highlight_start":1,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `anchor_lang::prelude::AccountInfo::<'a>::realloc`: Use AccountInfo::resize() instead\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprograms/uni-grading/src/lib.rs:5:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[program]\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ctx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"programs/uni-grading/src/lib.rs","byte_start":2541,"byte_end":2544,"line_start":73,"line_end":73,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        ctx: Context<GetClassroomInfo>","highlight_start":9,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"programs/uni-grading/src/lib.rs","byte_start":2541,"byte_end":2544,"line_start":73,"line_end":73,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        ctx: Context<GetClassroomInfo>","highlight_start":9,"highlight_end":12}],"label":null,"suggested_replacement":"_ctx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ctx`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprograms/uni-grading/src/lib.rs:73:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ctx: Context<GetClassroomInfo>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ctx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ctx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"programs/uni-grading/src/lib.rs","byte_start":2785,"byte_end":2788,"line_start":81,"line_end":81,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        ctx: Context<GetClassroomInfo>","highlight_start":9,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"programs/uni-grading/src/lib.rs","byte_start":2785,"byte_end":2788,"line_start":81,"line_end":81,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        ctx: Context<GetClassroomInfo>","highlight_start":9,"highlight_end":12}],"label":null,"suggested_replacement":"_ctx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ctx`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprograms/uni-grading/src/lib.rs:81:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ctx: Context<GetClassroomInfo>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ctx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ctx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"programs/uni-grading/src/lib.rs","byte_start":4790,"byte_end":4793,"line_start":136,"line_end":136,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        ctx: Context<GetStudentGrades>","highlight_start":9,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"programs/uni-grading/src/lib.rs","byte_start":4790,"byte_end":4793,"line_start":136,"line_end":136,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        ctx: Context<GetStudentGrades>","highlight_start":9,"highlight_end":12}],"label":null,"suggested_replacement":"_ctx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ctx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprograms/uni-grading/src/lib.rs:136:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ctx: Context<GetStudentGrades>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ctx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"6 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 6 warnings emitted\u001b[0m\n\n"}
