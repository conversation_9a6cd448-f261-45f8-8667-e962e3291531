import React, { useEffect, useState } from 'react';
import { Connection, PublicKey, clusterApiUrl } from '@solana/web3.js';
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { WalletProvider, ConnectionProvider, useWallet } from '@solana/wallet-adapter-react';
import { PhantomWalletAdapter, SolflareWalletAdapter } from '@solana/wallet-adapter-wallets';

const WalletConnect: React.FC = () => {
    const network = WalletAdapterNetwork.Devnet;
    const endpoint = clusterApiUrl(network);
    const wallets = [new PhantomWalletAdapter(), new SolflareWalletAdapter()];
    
    const [connected, setConnected] = useState(false);
    const { wallet, connect, disconnect } = useWallet();

    useEffect(() => {
        if (wallet && wallet.connected) {
            setConnected(true);
        } else {
            setConnected(false);
        }
    }, [wallet]);

    return (
        <div>
            {connected ? (
                <div>
                    <p>Connected to {wallet?.adapter.name}</p>
                    <button onClick={disconnect}>Disconnect</button>
                </div>
            ) : (
                <div>
                    <p>Connect your wallet</p>
                    <button onClick={connect}>Connect</button>
                </div>
            )}
        </div>
    );
};

const WalletConnectWrapper: React.FC = () => {
    return (
        <ConnectionProvider endpoint={clusterApiUrl('devnet')}>
            <WalletProvider wallets={wallets} autoConnect>
                <WalletConnect />
            </WalletProvider>
        </ConnectionProvider>
    );
};

export default WalletConnectWrapper;