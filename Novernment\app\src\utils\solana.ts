import { Connection, PublicKey, clusterApiUrl } from '@solana/web3.js';
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { getPhantomWallet } from '@solana/wallet-adapter-wallets';

const network = WalletAdapterNetwork.Devnet;
const connection = new Connection(clusterApiUrl(network), 'confirmed');

export const getWallet = () => {
    const wallets = [getPhantomWallet()];
    return wallets;
};

export const connectWallet = async (wallet) => {
    if (wallet) {
        try {
            await wallet.connect();
            console.log('Wallet connected:', wallet.publicKey.toString());
        } catch (error) {
            console.error('Failed to connect wallet:', error);
        }
    }
};

export const sendTransaction = async (transaction, wallet) => {
    try {
        const { signature } = await wallet.sendTransaction(transaction, connection);
        await connection.confirmTransaction(signature, 'processed');
        console.log('Transaction successful with signature:', signature);
    } catch (error) {
        console.error('Transaction failed:', error);
    }
};