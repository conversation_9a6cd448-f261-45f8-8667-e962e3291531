import React, { useState } from 'react';
import { Connection, PublicKey, clusterApiUrl } from '@solana/web3.js';
import { Program, Provider } from '@project-serum/anchor';
import { useWallet } from '@solana/wallet-adapter-react';
import { GradesClub } from '../../target/types/grades_club'; // Adjust the import based on your project structure

const GradeManager = () => {
    const wallet = useWallet();
    const [classroom, setClassroom] = useState<PublicKey | null>(null);
    const [studentName, setStudentName] = useState('');
    const [grade, setGrade] = useState('');

    const handleAddGrade = async () => {
        if (!wallet.publicKey || !classroom) return;

        const connection = new Connection(clusterApiUrl('devnet'), 'confirmed');
        const provider = new Provider(connection, wallet, {});
        const program = new Program<GradesClub>(idl, programId, provider);

        try {
            const tx = await program.rpc.addGrade(studentName, grade, {
                accounts: {
                    classroom: classroom,
                    authority: wallet.publicKey,
                },
            });
            console.log("Grade added with transaction:", tx);
        } catch (error) {
            console.error("Error adding grade:", error);
        }
    };

    return (
        <div>
            <h2>Grade Manager</h2>
            <input
                type="text"
                placeholder="Student Name"
                value={studentName}
                onChange={(e) => setStudentName(e.target.value)}
            />
            <input
                type="text"
                placeholder="Grade"
                value={grade}
                onChange={(e) => setGrade(e.target.value)}
            />
            <button onClick={handleAddGrade}>Add Grade</button>
        </div>
    );
};

export default GradeManager;