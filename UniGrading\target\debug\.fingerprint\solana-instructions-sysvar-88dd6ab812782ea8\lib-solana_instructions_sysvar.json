{"rustc": 383397013764560953, "features": "[]", "declared_features": "[\"dev-context-only-utils\"]", "target": 7758763119056657304, "profile": 66527933***********, "path": 2783725847648731302, "deps": [[4876843447960876563, "solana_serialize_utils", false, 6838185168632227991], [4917153833802766511, "solana_program_error", false, 6377321729773093048], [7896293946984509699, "bitflags", false, 11387477097062064726], [10570260326288551891, "solana_instruction", false, 6543215718921772342], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [14591356476411885690, "solana_sdk_ids", false, 1263336053477103343], [14666756292968957341, "solana_account_info", false, 13573356865800335666], [14762038526286640848, "solana_sysvar_id", false, 9885910766092997724], [15429715045911386410, "solana_sanitize", false, 4200197951219190093]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-instructions-sysvar-88dd6ab812782ea8/dep-lib-solana_instructions_sysvar", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}