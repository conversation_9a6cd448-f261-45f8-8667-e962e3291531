{"rustc": 383397013764560953, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 14640881849772911803, "deps": [[1573238666360410412, "rand_chacha", false, 6170925876358369764], [4684437522915235464, "libc", false, 13151738663823140194], [18130209639506977569, "rand_core", false, 7858307020392626866]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-6d6b1a2ff0ad4fcc/dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}