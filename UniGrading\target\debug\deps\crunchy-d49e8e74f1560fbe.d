/mnt/d/VSCode/UniGrading/target/debug/deps/crunchy-d49e8e74f1560fbe.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/crunchy-0.2.4/src/lib.rs /mnt/d/VSCode/UniGrading/target/debug/build/crunchy-94097bd9f01dc916/out/lib.rs

/mnt/d/VSCode/UniGrading/target/debug/deps/libcrunchy-d49e8e74f1560fbe.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/crunchy-0.2.4/src/lib.rs /mnt/d/VSCode/UniGrading/target/debug/build/crunchy-94097bd9f01dc916/out/lib.rs

/mnt/d/VSCode/UniGrading/target/debug/deps/libcrunchy-d49e8e74f1560fbe.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/crunchy-0.2.4/src/lib.rs /mnt/d/VSCode/UniGrading/target/debug/build/crunchy-94097bd9f01dc916/out/lib.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/crunchy-0.2.4/src/lib.rs:
/mnt/d/VSCode/UniGrading/target/debug/build/crunchy-94097bd9f01dc916/out/lib.rs:

# env-dep:CRUNCHY_LIB_SUFFIX=/lib.rs
# env-dep:OUT_DIR=/mnt/d/VSCode/UniGrading/target/debug/build/crunchy-94097bd9f01dc916/out
