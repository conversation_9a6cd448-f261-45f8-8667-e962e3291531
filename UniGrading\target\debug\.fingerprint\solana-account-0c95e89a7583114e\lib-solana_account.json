{"rustc": 383397013764560953, "features": "[]", "declared_features": "[\"bincode\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 15693990445969651597, "profile": 15657897354478470176, "path": 13405818471847651354, "deps": [[10570260326288551891, "solana_instruction", false, 6543215718921772342], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [14591356476411885690, "solana_sdk_ids", false, 1263336053477103343], [14666756292968957341, "solana_account_info", false, 13573356865800335666], [16041962814414187897, "solana_clock", false, 8585465566309786899]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-account-0c95e89a7583114e/dep-lib-solana_account", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}