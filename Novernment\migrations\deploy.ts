import { Program, Provider, web3 } from '@project-serum/anchor';
import { Connection, PublicKey } from '@solana/web3.js';
import { Novernment } from '../target/types/novernment';
import { clusterApiUrl } from '@solana/web3.js';

const main = async () => {
    const connection = new Connection(clusterApiUrl('devnet'), 'confirmed');
    const provider = Provider.local(clusterApiUrl('devnet'));
    const programId = new PublicKey('YOUR_PROGRAM_ID'); // Replace with your actual program ID
    const idl = await Program.fetchIdl(programId, provider);

    const program = new Program<Novernment>(idl, programId, provider);

    // Deploy logic here
    const tx = await program.rpc.initialize({
        accounts: {
            // Specify accounts needed for initialization
        },
    });

    console.log("Program deployed with transaction:", tx);
};

main().catch(err => {
    console.error(err);
});