import { Connection, PublicKey, clusterApiUrl } from '@solana/web3.js';
import { Program, Provider, web3 } from '@project-serum/anchor';
import { assert } from 'chai';
import { Novernment } from '../target/types/novernment';

describe('novernment', () => {
    const provider = Provider.local(clusterApiUrl('devnet'));
    const program = new Program<Novernment>(idl, programId, provider);

    it('Initializes a classroom', async () => {
        const classroomAccount = web3.Keypair.generate();
        const tx = await program.rpc.initializeClassroom("Math 101", {
            accounts: {
                classroom: classroomAccount.publicKey,
                authority: provider.wallet.publicKey,
                systemProgram: web3.SystemProgram.programId,
            },
            signers: [classroomAccount],
        });

        console.log("Classroom initialized with transaction:", tx);
        const classroom = await program.account.classroom.fetch(classroomAccount.publicKey);
        assert.equal(classroom.name, "Math 101");
    });

    it('Adds a student to the classroom', async () => {
        const classroomAccount = web3.Keypair.generate();
        await program.rpc.initializeClassroom("Science 101", {
            accounts: {
                classroom: classroomAccount.publicKey,
                authority: provider.wallet.publicKey,
                systemProgram: web3.SystemProgram.programId,
            },
            signers: [classroomAccount],
        });

        const studentAccount = web3.Keypair.generate();
        const tx = await program.rpc.addStudent("John Doe", {
            accounts: {
                classroom: classroomAccount.publicKey,
                student: studentAccount.publicKey,
                authority: provider.wallet.publicKey,
                systemProgram: web3.SystemProgram.programId,
            },
            signers: [studentAccount],
        });

        console.log("Student added with transaction:", tx);
        const student = await program.account.student.fetch(studentAccount.publicKey);
        assert.equal(student.name, "John Doe");
    });

    // Additional tests for managing grades and classrooms can be added here
});