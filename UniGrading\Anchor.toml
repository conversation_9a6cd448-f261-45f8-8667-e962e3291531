[toolchain]
anchor_version = "0.31.1"
package_manager = "yarn"

[features]
resolution = true
skip-lint = false
anchor-debug = true

[programs.localnet]
uni_grading = "********************************************"

[programs.devnet]
uni_grading = "********************************************"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "localnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"

[test]
startup_wait = 20000
