{"rustc": 383397013764560953, "features": "[\"alloc\", \"getrandom\", \"getrandom_package\", \"libc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 1928156412219526301, "deps": [[1333041802001714747, "rand_chacha", false, 8490940171011471126], [1740877332521282793, "rand_core", false, 15537643864707590751], [4684437522915235464, "libc", false, 13151738663823140194], [5170503507811329045, "getrandom_package", false, 7543190767988372493]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-6c435d91c698a6b4/dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}