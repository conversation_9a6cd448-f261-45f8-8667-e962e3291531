use anchor_lang::prelude::*;

declare_id!("YourProgramIdHere");

#[program]
pub mod novernment {
    use super::*;

    pub fn initialize_classroom(ctx: Context<InitializeClassroom>, classroom_name: String) -> ProgramResult {
        let classroom = &mut ctx.accounts.classroom;
        classroom.name = classroom_name;
        classroom.students = Vec::new();
        Ok(())
    }

    pub fn add_student(ctx: Context<AddStudent>, name: String) -> ProgramResult {
        let student = &mut ctx.accounts.student;
        student.name = name;
        let classroom = &mut ctx.accounts.classroom;
        classroom.students.push(student.key());
        Ok(())
    }

    pub fn assign_grade(ctx: Context<AssignGrade>, student: Pubkey, grade: u8) -> ProgramResult {
        let classroom = &mut ctx.accounts.classroom;
        let student_account = &mut ctx.accounts.student;

        if !classroom.students.contains(&student) {
            return Err(ErrorCode::StudentNotInClassroom.into());
        }

        student_account.grade = grade;
        Ok(())
    }
}

#[derive(Accounts)]
pub struct InitializeClassroom<'info> {
    #[account(init, payer = authority, space = 8 + 40)]
    pub classroom: Account<'info, Classroom>,
    #[account(mut)]
    pub authority: Signer<'info>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct AddStudent<'info> {
    #[account(mut)]
    pub classroom: Account<'info, Classroom>,
    #[account(init, payer = authority, space = 8 + 40)]
    pub student: Account<'info, Student>,
    #[account(mut)]
    pub authority: Signer<'info>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct AssignGrade<'info> {
    #[account(mut)]
    pub classroom: Account<'info, Classroom>,
    #[account(mut)]
    pub student: Account<'info, Student>,
}

#[account]
pub struct Classroom {
    pub name: String,
    pub students: Vec<Pubkey>,
}

#[account]
pub struct Student {
    pub name: String,
    pub grade: u8,
}

#[error]
pub enum ErrorCode {
    #[msg("The student is not in the classroom.")]
    StudentNotInClassroom,
}