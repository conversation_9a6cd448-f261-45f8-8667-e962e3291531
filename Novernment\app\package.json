{"name": "novernment", "version": "1.0.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "react-scripts": "4.0.3", "@solana/web3.js": "^1.30.0", "@solana/wallet-adapter-react": "^0.15.0", "@solana/wallet-adapter-wallets": "^0.15.0", "@solana/wallet-adapter-ant-design": "^0.15.0", "antd": "^4.16.13"}, "devDependencies": {"typescript": "^4.1.3", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2"}}