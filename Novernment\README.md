# Novernment Project

Novernment is a decentralized application built on the Solana blockchain for managing grades and classrooms. This project utilizes the Anchor framework for the backend and React.js for the frontend, providing a seamless experience for users to manage educational data.

## Project Structure

The project is organized into the following main directories:

- **app**: Contains the frontend React application.
  - **public**: Static files, including the main HTML entry point.
  - **src**: Source code for the React application, including components, pages, and utilities.
- **programs**: Contains the Solana program written in Rust, implementing the backend logic for managing classrooms and grades.
- **migrations**: Scripts for deploying the Solana program to the blockchain.
- **tests**: Contains tests for the Solana program to ensure functionality.
- **Anchor.toml**: Configuration file for the Anchor framework.
- **package.json**: Configuration file for npm at the root level.

## Getting Started

To get started with the Novernment project, follow these steps:

1. **Clone the repository**:
   ```
   git clone <repository-url>
   cd novernment
   ```

2. **Install dependencies**:
   Navigate to the `app` directory and install the frontend dependencies:
   ```
   cd app
   npm install
   ```

3. **Build and deploy the Solana program**:
   Navigate to the `programs/novernment` directory and build the Rust program:
   ```
   cd programs/novernment
   anchor build
   ```

4. **Deploy the program to the Solana blockchain**:
   Run the deployment script located in the `migrations` directory:
   ```
   cd ../../migrations
   ts-node deploy.ts
   ```

5. **Run the frontend application**:
   Navigate back to the `app` directory and start the development server:
   ```
   cd ../app
   npm start
   ```

## Features

- **Classroom Management**: Create and manage classrooms, view enrolled students, and track grades.
- **Grade Management**: Input and manage grades for students within each classroom.
- **Wallet Integration**: Connect to Solana wallets (e.g., Phantom, Solflare) for secure transactions.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.