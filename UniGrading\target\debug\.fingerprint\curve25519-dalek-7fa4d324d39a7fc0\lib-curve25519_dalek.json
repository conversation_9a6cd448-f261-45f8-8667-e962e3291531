{"rustc": 383397013764560953, "features": "[\"alloc\", \"default\", \"digest\", \"precomputed-tables\", \"rand_core\", \"serde\", \"zeroize\"]", "declared_features": "[\"alloc\", \"default\", \"digest\", \"ff\", \"group\", \"group-bits\", \"legacy_compatibility\", \"precomputed-tables\", \"rand_core\", \"serde\", \"zeroize\"]", "target": 115635582535548150, "profile": 15657897354478470176, "path": 6674524644462127670, "deps": [[1513171335889705703, "curve25519_dalek_derive", false, 6433689098295350555], [2828590642173593838, "cfg_if", false, 3792137565522295096], [6528079939221783635, "zeroize", false, 16980799361177092455], [9689903380558560274, "serde", false, 8537716410839897940], [13595581133353633439, "build_script_build", false, 9844542998962132706], [17003143334332120809, "subtle", false, 1042924105697762283], [17475753849556516473, "digest", false, 14505440863905975160], [17620084158052398167, "cpufeatures", false, 15888015519599623953], [18130209639506977569, "rand_core", false, 7858307020392626866]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/curve25519-dalek-7fa4d324d39a7fc0/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}