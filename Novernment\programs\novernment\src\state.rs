// This file defines the data structures and state management for the Solana program, including classroom and student data.

use anchor_lang::prelude::*;

#[account]
pub struct Classroom {
    pub name: String,
    pub authority: Pubkey,
    pub student_count: u32,
}

#[account]
pub struct Student {
    pub name: String,
    pub classroom: Pubkey,
    pub grades: Vec<u8>, // Assuming grades are stored as a vector of u8
}

impl Classroom {
    pub const LEN: usize = 8 + 32 + 4 + 4; // Discriminator + authority + student_count + name length
}

impl Student {
    pub const LEN: usize = 8 + 32 + 4 + 4; // Discriminator + classroom + grades length + name length
}